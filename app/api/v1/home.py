from fastapi import APIRouter, HTTPException, status, Request, Query
from sqlalchemy.orm import Session
from app.core.database import get_db_session, close_db_session
from app.core.auth import extract_token_from_request, get_current_user
from app.crud import family_member as crud_family_member
from app.crud import health_data as crud_health_data
from typing import Optional, Dict, Any
import datetime

router = APIRouter()

@router.get("/reports/{uid}")
async def get_user_history_reports(
    uid: int,
    request: Request,
    limit: Optional[int] = Query(10, description="限制返回数量"),
    offset: Optional[int] = Query(0, description="偏移量")
) -> Dict[str, Any]:
    """获取用户历史健康报告接口"""
    print(f"=== 获取历史报告请求 ===")
    print(f"请求的uid: {uid}")
    print(f"limit: {limit}, offset: {offset}")

    # 验证token并获取当前用户信息
    token = extract_token_from_request(request)
    print(f"提取的token: {token[:20]}..." if token else "无token")

    current_user = get_current_user(token)
    print(f"当前用户: {current_user}")

    db = get_db_session()
    try:
        # 验证用户权限
        current_user_id = int(current_user.get("sub", 0))
        print(f"当前用户ID: {current_user_id}")
        print(f"请求的用户ID: {uid}")

        if uid != current_user_id:
            print(f"权限验证失败: {uid} != {current_user_id}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权限访问其他用户的数据"
            )

        # 获取用户的"本人"家庭成员记录
        print(f"查找用户 {uid} 的'本人'家庭成员记录...")
        self_member = crud_family_member.get_family_member_by_relationship(
            db, uid=uid, relationship="本人"
        )
        print(f"找到的家庭成员: {self_member}")

        if not self_member:
            print("未找到'本人'家庭成员记录")
            return {
                "reports": [],
                "total": 0,
                "message": "请先添加个人信息"
            }

        # 获取健康数据记录
        health_records = crud_health_data.get_health_reports_by_fuid(
            db, fuid=self_member.fuid, limit=limit, offset=offset
        )

        # 获取总数
        total_count = crud_health_data.get_health_reports_count_by_fuid(
            db, fuid=self_member.fuid
        )

        # 格式化返回数据
        reports = []
        for record in health_records:
            reports.append({
                "id": record.id,
                "report_id": record.report_id,
                "detection_time": record.create_time.isoformat() if record.create_time else None,
                "name": record.name,
                "gender": record.gender,
                "age": record.age,
                "height": record.height,
                "weight": record.weight,
                "bmi": record.bmi,
                "heart_rate": record.heart_rate,
                "spo2": record.spo2,
                "breathing_rate": record.breathing_rate,
                "pulse_rate": record.pulse_rate,
                "blood_pressure": record.blood_pressure,
                "hemoglobin": record.hemoglobin,
                "signal_quality": record.signal_quality,
                "cardiac_risk": record.cardiac_risk,
                "brain_risk": record.brain_risk,
                "atrial_fibrillation_risk": record.afib,
                "arrhythmia_risk": record.arrhythmia,
                "anemia_status": record.anemia,
                "risk_assessment": record.risk_assessment,
                "hrv_time_domain": record.hrv_time_domain,
                "hrv_frequency_domain": record.hrv_frequency_domain,
                "hrv_nonlinear": record.hrv_nonlinear,
                "hrv": record.hrv
            })

        return {
            "reports": reports,
            "total": total_count,
            "limit": limit,
            "offset": offset
        }

    except Exception as e:
        print(f"获取历史报告时发生错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取历史报告失败: {str(e)}"
        )
    finally:
        close_db_session(db)

@router.get("/family-reports/{uid}/{fuid}")
async def get_family_member_history_reports(
    uid: int,
    fuid: int,
    request: Request,
    limit: Optional[int] = Query(10, description="限制返回数量"),
    offset: Optional[int] = Query(0, description="偏移量")
) -> Dict[str, Any]:
    """获取家庭成员历史健康报告接口"""
    print(f"=== 获取家庭成员历史报告请求 ===")
    print(f"请求的uid: {uid}, fuid: {fuid}")
    print(f"limit: {limit}, offset: {offset}")

    # 验证token并获取当前用户信息
    token = extract_token_from_request(request)
    current_user = get_current_user(token)

    db = get_db_session()
    try:
        # 验证用户权限
        current_user_id = int(current_user.get("sub", 0))
        print(f"当前用户ID: {current_user_id}")
        if uid != current_user_id:
            print(f"权限验证失败: {uid} != {current_user_id}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权限访问其他用户的数据"
            )

        # 验证家庭成员关系
        print(f"查找家庭成员 fuid: {fuid}")
        family_member = crud_family_member.get_family_member_by_id(db, fuid)
        print(f"找到的家庭成员: {family_member}")
        if not family_member:
            print(f"家庭成员不存在: fuid={fuid}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="家庭成员不存在"
            )
        if family_member.uid != uid:
            print(f"家庭成员权限验证失败: {family_member.uid} != {uid}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="无权限访问此家庭成员"
            )

        # 获取健康数据记录
        print(f"查询健康数据记录: fuid={fuid}")
        health_records = crud_health_data.get_health_reports_by_fuid(
            db, fuid=fuid, limit=limit, offset=offset
        )
        print(f"找到的健康记录数量: {len(health_records)}")

        # 获取总数
        total_count = crud_health_data.get_health_reports_count_by_fuid(
            db, fuid=fuid
        )
        print(f"总记录数: {total_count}")

        # 格式化返回数据
        reports = []
        for record in health_records:
            reports.append({
                "id": record.id,
                "report_id": record.report_id,
                "name": record.name,
                "gender": record.gender,
                "age": record.age,
                "height": record.height,
                "weight": record.weight,
                "bmi": record.bmi,
                "detection_time": record.create_time.isoformat() if record.create_time else None,
                "create_time": record.create_time.isoformat() if record.create_time else None,
                "heart_rate": record.heart_rate,
                "spo2": record.spo2,
                "blood_pressure": record.blood_pressure,
                "breathing_rate": record.breathing_rate,
                "pulse_rate": record.pulse_rate,
                "hemoglobin": record.hemoglobin,
                "signal_quality": record.signal_quality,
                "cardiac_risk": record.cardiac_risk,
                "brain_risk": record.brain_risk,
                "atrial_fibrillation_risk": record.afib,
                "arrhythmia_risk": record.arrhythmia,
                "anemia_status": record.anemia,
                "risk_assessment": record.risk_assessment
            })

        return {
            "reports": reports,
            "total": total_count,
            "limit": limit,
            "offset": offset,
            "family_member": {
                "fuid": family_member.fuid,
                "name": family_member.name,
                "relationship": family_member.relationship
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"获取家庭成员历史报告异常: {str(e)}")
        print(f"异常类型: {type(e)}")
        import traceback
        print(f"异常堆栈: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取家庭成员历史报告失败: {str(e)}"
        )
    finally:
        close_db_session(db)

@router.get("/{uid}")
@router.get("/{uid}/{fuid}")
async def get_home_data(
    uid: int,
    request: Request,
    fuid: Optional[int] = None
) -> Dict[str, Any]:
    """获取首页数据接口 - 需要token认证"""
    # 验证token并获取当前用户信息
    token = extract_token_from_request(request)
    current_user = get_current_user(token)
    
    db = get_db_session()
    try:
        # 验证用户权限
        current_user_id = int(current_user.get("sub", 0))
        if uid != current_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问其他用户的数据"
            )

        # 初始化默认返回数据（只包含实际存在的字段）
        family_member_data = {
            "fuid": 0,
            "uid": uid,
            "relationship": "",
            "name": "",
            "gender": "",
            "height": 0.0,
            "weight": 0.0,
            "birth_year": 0,
            "age": 0,  # 计算得出的年龄
            "avatar_url": ""
        }
        
        health_report_data = None
        bvp_waveform_data = None

        # 如果指定了fuid，获取家庭成员信息
        if fuid is not None:
            family_member = crud_family_member.get_family_member_by_fuid(db, fuid)
            if family_member and family_member.uid == uid:
                # 计算年龄
                current_year = datetime.datetime.now().year
                age = current_year - family_member.birth_year if family_member.birth_year else 0
                
                family_member_data = {
                    "fuid": family_member.fuid,
                    "uid": family_member.uid,
                    "relationship": family_member.relationship or "",
                    "name": family_member.name or "",
                    "gender": family_member.gender or "",
                    "height": family_member.height or 0.0,
                    "weight": family_member.weight or 0.0,
                    "birth_year": family_member.birth_year or 0,
                    "age": age,
                    "avatar_url": family_member.avatar_url or ""
                }
                
                # 获取该家庭成员的最新健康数据
                health_report_data = crud_health_data.get_latest_health_report_by_fuid(db, uid, fuid)
                if health_report_data:
                    bvp_waveform_data = crud_health_data.get_bvp_waveform_by_report_id(db, health_report_data.report_id)
        else:
            # fuid为None，获取用户本人的最新健康数据
            health_report_data = crud_health_data.get_latest_health_report_by_uid(db, uid)
            if health_report_data:
                bvp_waveform_data = crud_health_data.get_bvp_waveform_by_report_id(db, health_report_data.report_id)
        
        # 序列化健康报告数据
        health_report_serialized = None
        if health_report_data:
            health_report_serialized = {
                "id": health_report_data.id,
                "report_id": health_report_data.report_id,
                "uid": health_report_data.uid,
                "fuid": health_report_data.fuid,
                "name": health_report_data.name,
                "gender": health_report_data.gender,
                "age": health_report_data.age,
                "height": health_report_data.height,
                "weight": health_report_data.weight,
                "bmi": health_report_data.bmi,
                "heart_rate": health_report_data.heart_rate,
                "pulse_rate": health_report_data.pulse_rate,
                "blood_pressure": health_report_data.blood_pressure,
                # 为了兼容性，同时提供分离的血压字段
                "systolic_pressure": health_report_data.blood_pressure.get('SBP') if health_report_data.blood_pressure else None,
                "diastolic_pressure": health_report_data.blood_pressure.get('DBP') if health_report_data.blood_pressure else None,
                "spo2": health_report_data.spo2,
                "breathing_rate": health_report_data.breathing_rate,
                "cardiac_risk": health_report_data.cardiac_risk,
                "brain_risk": health_report_data.brain_risk,
                "afib": health_report_data.afib,
                "arrhythmia": health_report_data.arrhythmia,
                "anemia": health_report_data.anemia,
                "hemoglobin": health_report_data.hemoglobin,
                "risk_assessment": health_report_data.risk_assessment,
                "signal_quality": health_report_data.signal_quality,
                "hrv_time_domain": health_report_data.hrv_time_domain,
                "hrv_frequency_domain": health_report_data.hrv_frequency_domain,
                "hrv_nonlinear": health_report_data.hrv_nonlinear,
                "hrv": health_report_data.hrv,
                "extra": health_report_data.extra,
                "create_time": health_report_data.create_time.isoformat() if health_report_data.create_time else None,
                "update_time": health_report_data.update_time.isoformat() if health_report_data.update_time else None
            }

        # 序列化BVP波形数据
        bvp_waveform_serialized = None
        if bvp_waveform_data:
            bvp_waveform_serialized = {
                "id": bvp_waveform_data.id,
                "report_id": bvp_waveform_data.report_id,
                "bvp": bvp_waveform_data.bvp,
                "timestamps": bvp_waveform_data.timestamps,
                "sampling_rate": bvp_waveform_data.sampling_rate,
                "create_time": bvp_waveform_data.create_time.isoformat() if bvp_waveform_data.create_time else None,
                "update_time": bvp_waveform_data.update_time.isoformat() if bvp_waveform_data.update_time else None
            }

        return {
            "family_member": family_member_data,
            "health_report": health_report_serialized,
            "bvp_waveform": bvp_waveform_serialized,
            "metadata": {
                "uid": uid,
                "fuid": fuid,
                "has_health_data": health_report_data is not None,
                "has_bvp_data": bvp_waveform_data is not None,
                "is_family_member": fuid is not None,
                "query_time": datetime.datetime.now().isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取首页数据失败: {str(e)}"
        )
    finally:
        close_db_session(db)

